import React from 'react';
import { StyleSheet, Text, View, Pressable } from 'react-native';
import { Link, Stack, useRouter } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { CircleAlert as AlertCircle, Chrome as Home } from 'lucide-react-native';

export default function NotFoundScreen() {
  const theme = useTheme();
  const router = useRouter();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
      backgroundColor: theme.colors.background,
    },
    iconContainer: {
      marginBottom: 24,
    },
    text: {
      fontSize: 24,
      fontFamily: 'Roboto-Bold',
      color: theme.colors.onBackground,
      marginBottom: 12,
    },
    subText: {
      fontSize: 16,
      fontFamily: 'Roboto-Regular',
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 32,
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 28,
    },
    buttonText: {
      color: theme.colors.onPrimary,
      fontFamily: 'Roboto-Medium',
      fontSize: 16,
      marginLeft: 8,
    },
  });

  return (
    <>
      <Stack.Screen options={{ title: 'Not Found' }} />
      <View style={styles.container}>
        <View style={styles.iconContainer}>
          <AlertCircle size={64} color={theme.colors.error} />
        </View>
        <Text style={styles.text}>Page Not Found</Text>
        <Text style={styles.subText}>
          The page you're looking for doesn't exist or has been moved.
        </Text>
        <Pressable 
          style={styles.button} 
          onPress={() => router.navigate('/')}
        >
          <Home size={20} color={theme.colors.onPrimary} />
          <Text style={styles.buttonText}>Go to Home</Text>
        </Pressable>
      </View>
    </>
  );
}

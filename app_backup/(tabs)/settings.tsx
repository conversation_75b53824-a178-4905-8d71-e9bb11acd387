"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, Pressable, Switch, ActivityIndicator, Image } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { StatusBar } from "expo-status-bar"
import { useTheme } from "@/hooks/useTheme"
import {
  LogOut,
  User,
  CreditCard,
  History,
  Bell,
  Shield,
  ChevronRight,
  Trash2,
  ShieldCheck,
  ExternalLink,
} from "lucide-react-native"
import { useRouter } from "expo-router"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { supabase } from "@/lib/supabase"
import { useChatHistory } from "@/hooks/useChatHistory"
import AuthScreen from "@/components/auth/AuthScreen"
import { useSubscription } from "@/hooks/useSubscription"
import { format } from "date-fns"
import FadeIn from "@/components/animations/FadeIn"

export default function SettingsScreen() {
  const theme = useTheme()
  const router = useRouter()
  const { chatHistory, deleteChat } = useChatHistory()
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState(null)
  const [notifications, setNotifications] = useState(true)
  const [darkMode, setDarkMode] = useState(false)
  const { subscription, refresh: refreshSubscription } = useSubscription()

  useEffect(() => {
    checkUser()
    loadSettings()
  }, [])

  const checkUser = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      setUser(user)
    } catch (error) {
      console.error("Error checking user:", error)
    }
  }

  const loadSettings = async () => {
    try {
      const notifSetting = await AsyncStorage.getItem("notifications")
      const themeSetting = await AsyncStorage.getItem("darkMode")
      setNotifications(notifSetting !== "false")
      setDarkMode(themeSetting === "true")
    } catch (error) {
      console.error("Error loading settings:", error)
    }
  }

  const handleSignOut = async () => {
    setLoading(true)
    try {
      await supabase.auth.signOut()
      router.replace("/")
    } catch (error) {
      console.error("Error signing out:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleClearHistory = async () => {
    try {
      for (const chat of chatHistory) {
        await deleteChat(chat.id)
      }
      await AsyncStorage.removeItem("medical_advisor_chat_history")
    } catch (error) {
      console.error("Error clearing history:", error)
    }
  }

  const toggleNotifications = async (value) => {
    try {
      await AsyncStorage.setItem("notifications", value.toString())
      setNotifications(value)
    } catch (error) {
      console.error("Error saving notification setting:", error)
    }
  }

  const toggleDarkMode = async (value) => {
    try {
      await AsyncStorage.setItem("darkMode", value.toString())
      setDarkMode(value)
    } catch (error) {
      console.error("Error saving theme setting:", error)
    }
  }

  const navigateToAdFreePurchase = () => {
    router.push("/purchase-ad-free")
  }

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
    },
    headerContainer: {
      position: "relative",
      overflow: "hidden",
      borderBottomLeftRadius: 24,
      borderBottomRightRadius: 24,
      marginBottom: 24,
    },
    headerBackground: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: "100%",
      height: "100%",
    },
    headerOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    header: {
      padding: 24,
      paddingTop: 48,
      paddingBottom: 32,
      zIndex: 1,
    },
    title: {
      fontSize: 32,
      fontFamily: "Roboto-Bold",
      color: "#FFFFFF",
      marginBottom: 16,
      textShadowColor: "rgba(0, 0, 0, 0.75)",
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 4,
    },
    profileContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 16,
    },
    profileImage: {
      width: 64,
      height: 64,
      borderRadius: 32,
      marginRight: 16,
      borderWidth: 2,
      borderColor: "#FFFFFF",
    },
    profileInfo: {
      flex: 1,
    },
    profileName: {
      fontSize: 20,
      fontFamily: "Roboto-Bold",
      color: "#FFFFFF",
      marginBottom: 4,
      textShadowColor: "rgba(0, 0, 0, 0.75)",
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 4,
    },
    profileEmail: {
      fontSize: 14,
      fontFamily: "Roboto-Regular",
      color: "#FFFFFF",
      opacity: 0.9,
      textShadowColor: "rgba(0, 0, 0, 0.75)",
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 4,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 14,
      fontFamily: "Roboto-Medium",
      color: theme.colors.primary,
      marginLeft: 16,
      marginBottom: 8,
      marginTop: 24,
      textTransform: "uppercase",
      letterSpacing: 0.5,
    },
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      marginHorizontal: 16,
      marginBottom: 12,
      padding: 16,
      flexDirection: "row",
      alignItems: "center",
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      borderWidth: 1,
      borderColor: theme.colors.outlineVariant,
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.primaryContainer,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 16,
    },
    cardContent: {
      flex: 1,
    },
    cardTitle: {
      fontSize: 16,
      fontFamily: "Roboto-Medium",
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    cardSubtitle: {
      fontSize: 14,
      fontFamily: "Roboto-Regular",
      color: theme.colors.onSurfaceVariant,
    },
    switchContainer: {
      marginLeft: 8,
    },
    button: {
      backgroundColor: theme.colors.errorContainer,
      borderRadius: 28,
      padding: 16,
      marginHorizontal: 16,
      marginTop: 24,
      marginBottom: 32,
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    buttonText: {
      color: theme.colors.onErrorContainer,
      fontSize: 16,
      fontFamily: "Roboto-Medium",
      marginLeft: 8,
    },
    subscriptionBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      backgroundColor: theme.colors.primaryContainer,
      marginLeft: 8,
    },
    subscriptionText: {
      fontSize: 12,
      fontFamily: "Roboto-Medium",
      color: theme.colors.onPrimaryContainer,
    },
    subscriptionContainer: {
      padding: 16,
    },
    purchaseButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 28,
      padding: 16,
      marginHorizontal: 16,
      marginTop: 8,
      marginBottom: 16,
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    purchaseButtonText: {
      color: theme.colors.onPrimary,
      fontSize: 16,
      fontFamily: "Roboto-Medium",
      marginLeft: 8,
    },
  })

  if (!user) {
    return <AuthScreen />
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" />
      <View style={styles.container}>
        <FadeIn delay={100}>
          <View style={styles.headerContainer}>
            <Image
              source={require("@/assets/images/doctor-consultation.jpg")}
              style={styles.headerBackground}
              resizeMode="cover"
            />
            <View style={styles.headerOverlay} />
            <View style={styles.header}>
              <Text style={styles.title}>Settings</Text>
              <View style={styles.profileContainer}>
                <Image
                  source={require("@/assets/images/health-monitoring.jpg")}
                  style={styles.profileImage}
                  resizeMode="cover"
                />
                <View style={styles.profileInfo}>
                  <Text style={styles.profileName}>{user.email?.split("@")[0] || "User"}</Text>
                  <Text style={styles.profileEmail}>{user.email}</Text>
                </View>
              </View>
            </View>
          </View>
        </FadeIn>

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Account Section */}
          <FadeIn delay={200}>
            <Text style={styles.sectionTitle}>Account</Text>
            <Pressable style={styles.card} android_ripple={{ color: theme.colors.ripple }}>
              <View style={styles.iconContainer}>
                <User size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>Profile</Text>
                <Text style={styles.cardSubtitle}>
                  {subscription?.status === "active" ? "Premium Member" : "Free Plan"}
                </Text>
              </View>
              <ChevronRight size={20} color={theme.colors.onSurfaceVariant} />
            </Pressable>
          </FadeIn>

          {/* Subscription Section */}
          <FadeIn delay={300}>
            <Text style={styles.sectionTitle}>Subscription</Text>

            {subscription?.isActive ? (
              <View style={styles.card}>
                <View style={styles.iconContainer}>
                  <ShieldCheck size={24} color={theme.colors.primary} />
                </View>
                <View style={styles.cardContent}>
                  <Text style={styles.cardTitle}>Ad-Free Experience</Text>
                  <Text style={styles.cardSubtitle}>
                    {subscription.status === "lifetime"
                      ? "Lifetime Access"
                      : `Active until ${format(subscription.currentPeriodEnd, "MMM d, yyyy")}`}
                  </Text>
                </View>
                <View style={styles.subscriptionBadge}>
                  <Text style={styles.subscriptionText}>
                    {subscription.status === "lifetime" ? "LIFETIME" : "ACTIVE"}
                  </Text>
                </View>
              </View>
            ) : (
              <Pressable
                style={styles.purchaseButton}
                onPress={navigateToAdFreePurchase}
                android_ripple={{ color: "rgba(255, 255, 255, 0.2)" }}
              >
                <ShieldCheck size={20} color={theme.colors.onPrimary} />
                <Text style={styles.purchaseButtonText}>Purchase Ad-free Premium</Text>
              </Pressable>
            )}

            <Pressable
              style={styles.card}
              onPress={navigateToAdFreePurchase}
              android_ripple={{ color: theme.colors.ripple }}
            >
              <View style={styles.iconContainer}>
                <CreditCard size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>Subscription Options</Text>
                <Text style={styles.cardSubtitle}>
                  {subscription?.isActive ? "Manage your subscription" : "View available plans"}
                </Text>
              </View>
              <ExternalLink size={20} color={theme.colors.onSurfaceVariant} />
            </Pressable>
          </FadeIn>

          {/* Data & Privacy Section */}
          <FadeIn delay={400}>
            <Text style={styles.sectionTitle}>Data & Privacy</Text>
            <Pressable style={styles.card} android_ripple={{ color: theme.colors.ripple }} onPress={handleClearHistory}>
              <View style={styles.iconContainer}>
                <History size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>Chat History</Text>
                <Text style={styles.cardSubtitle}>{chatHistory.length} conversations saved</Text>
              </View>
              <Trash2 size={20} color={theme.colors.error} />
            </Pressable>
          </FadeIn>

          {/* Preferences Section */}
          <FadeIn delay={500}>
            <Text style={styles.sectionTitle}>Preferences</Text>
            <View style={styles.card}>
              <View style={styles.iconContainer}>
                <Bell size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>Notifications</Text>
                <Text style={styles.cardSubtitle}>Push notifications and alerts</Text>
              </View>
              <View style={styles.switchContainer}>
                <Switch
                  value={notifications}
                  onValueChange={toggleNotifications}
                  trackColor={{
                    false: theme.colors.surfaceVariant,
                    true: theme.colors.primaryContainer,
                  }}
                  thumbColor={notifications ? theme.colors.primary : theme.colors.outline}
                  ios_backgroundColor={theme.colors.surfaceVariant}
                />
              </View>
            </View>

            <View style={styles.card}>
              <View style={styles.iconContainer}>
                <Shield size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>Dark Mode</Text>
                <Text style={styles.cardSubtitle}>Toggle dark theme</Text>
              </View>
              <View style={styles.switchContainer}>
                <Switch
                  value={darkMode}
                  onValueChange={toggleDarkMode}
                  trackColor={{
                    false: theme.colors.surfaceVariant,
                    true: theme.colors.primaryContainer,
                  }}
                  thumbColor={darkMode ? theme.colors.primary : theme.colors.outline}
                  ios_backgroundColor={theme.colors.surfaceVariant}
                />
              </View>
            </View>
          </FadeIn>

          <FadeIn delay={600}>
            <Pressable
              style={styles.button}
              onPress={handleSignOut}
              disabled={loading}
              android_ripple={{ color: theme.colors.ripple }}
            >
              {loading ? (
                <ActivityIndicator color={theme.colors.onErrorContainer} />
              ) : (
                <>
                  <LogOut size={20} color={theme.colors.onErrorContainer} />
                  <Text style={styles.buttonText}>Sign Out</Text>
                </>
              )}
            </Pressable>
          </FadeIn>
        </ScrollView>
      </View>
    </SafeAreaView>
  )
}

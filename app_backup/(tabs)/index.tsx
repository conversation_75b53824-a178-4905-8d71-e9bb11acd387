"use client"

import { useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, Image, Pressable, useWindowDimensions, Platform } from "react-native"
import { useRouter } from "expo-router"
import { SafeAreaView } from "react-native-safe-area-context"
import { useTheme } from "@/hooks/useTheme"
import { StatusBar } from "expo-status-bar"
import { ArrowRight, Heart, Activity, Stethoscope } from "lucide-react-native"
import FadeIn from "@/components/animations/FadeIn"
import BannerAdView from "@/components/ads/BannerAdView"
import { useAdManager } from "@/hooks/useAdManager"
import { BannerAdSize } from "react-native-google-mobile-ads"

export default function LandingScreen() {
  const router = useRouter()
  const theme = useTheme()
  const { width } = useWindowDimensions()
  const isTablet = width > 768
  const { showInterstitialAd, showAds } = useAdManager()

  useEffect(() => {
    // Show interstitial ad when the app first launches (with 10% probability)
    if (Math.random() < 0.1) {
      showInterstitialAd()
    }
  }, [])

  const navigateToChat = async () => {
    // Show interstitial ad with 25% probability when starting a chat
    if (Math.random() < 0.25) {
      const adShown = await showInterstitialAd()
      // Only navigate after ad is shown or if ad fails to show
      setTimeout(
        () => {
          router.navigate("/chat")
        },
        adShown ? 500 : 0,
      )
    } else {
      router.navigate("/chat")
    }
  }

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: showAds ? 50 : 0,
    },
    heroContainer: {
      padding: 0,
      paddingTop: 0,
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 32,
      position: "relative",
    },
    heroContent: {
      maxWidth: 600,
      width: "100%",
      position: "relative",
      zIndex: 2,
      padding: 24,
      paddingTop: Platform.OS === "web" ? 120 : 60,
    },
    heroImageContainer: {
      position: "relative",
      width: "100%",
      height: isTablet ? 500 : 400,
      overflow: "hidden",
    },
    heroImage: {
      width: "100%",
      height: "100%",
      position: "absolute",
    },
    heroOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.4)",
      zIndex: 1,
    },
    title: {
      fontFamily: "Roboto-Bold",
      fontSize: isTablet ? 48 : 36,
      color: "#FFFFFF",
      textAlign: "center",
      marginBottom: 16,
      lineHeight: isTablet ? 56 : 44,
      textShadowColor: "rgba(0, 0, 0, 0.75)",
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 4,
    },
    subtitle: {
      fontFamily: "Roboto-Regular",
      fontSize: isTablet ? 20 : 18,
      color: "#FFFFFF",
      textAlign: "center",
      marginBottom: 32,
      lineHeight: isTablet ? 30 : 28,
      textShadowColor: "rgba(0, 0, 0, 0.75)",
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 4,
    },
    buttonContainer: {
      marginTop: 16,
      alignItems: "center",
    },
    chatButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderRadius: 28,
      minWidth: 200,
      elevation: 4,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
    },
    chatButtonText: {
      fontFamily: "Roboto-Medium",
      color: theme.colors.onPrimary,
      fontSize: 16,
      marginRight: 8,
    },
    featuresContainer: {
      padding: 24,
      backgroundColor: theme.colors.surfaceContainerLow,
      borderTopLeftRadius: 32,
      borderTopRightRadius: 32,
      marginTop: -32,
      paddingTop: 48,
    },
    featuresTitle: {
      fontFamily: "Roboto-Bold",
      fontSize: 28,
      color: theme.colors.onSurface,
      marginBottom: 24,
      textAlign: "center",
    },
    featuresGrid: {
      flexDirection: isTablet ? "row" : "column",
      flexWrap: "wrap",
      justifyContent: "center",
    },
    featureCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 8,
      width: isTablet ? "30%" : "100%",
      minWidth: isTablet ? 220 : "auto",
      alignItems: "flex-start",
      elevation: 3,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.outlineVariant,
    },
    featureIconContainer: {
      backgroundColor: theme.colors.primaryContainer,
      width: 64,
      height: 64,
      borderRadius: 32,
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 16,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    featureTitle: {
      fontFamily: "Roboto-Bold",
      fontSize: 20,
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    featureDescription: {
      fontFamily: "Roboto-Regular",
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      lineHeight: 24,
    },
    footer: {
      padding: 24,
      backgroundColor: theme.colors.surfaceContainerHigh,
      alignItems: "center",
    },
    disclaimer: {
      fontFamily: "Roboto-Regular",
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: "center",
      marginBottom: 16,
      maxWidth: 600,
      lineHeight: 20,
    },
    links: {
      flexDirection: "row",
      marginTop: 8,
    },
    link: {
      fontFamily: "Roboto-Medium",
      fontSize: 14,
      color: theme.colors.primary,
      marginHorizontal: 8,
      textDecorationLine: "underline",
    },
    midPageAdContainer: {
      width: "100%",
      alignItems: "center",
      marginVertical: 24,
    },
  })

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" />
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.heroContainer}>
            <View style={styles.heroImageContainer}>
              <Image source={require("@/assets/images/medical-hero.jpg")} style={styles.heroImage} resizeMode="cover" />
              <View style={styles.heroOverlay} />
            </View>
            <View style={styles.heroContent}>
              <FadeIn delay={100}>
                <Text style={styles.title}>Your Personal Medical Advisor</Text>
                <Text style={styles.subtitle}>
                  Get reliable health information and guidance through our AI-powered medical assistant. Always
                  available to help with your health questions.
                </Text>
                <View style={styles.buttonContainer}>
                  <Pressable
                    style={styles.chatButton}
                    onPress={navigateToChat}
                    android_ripple={{ color: "rgba(255, 255, 255, 0.2)" }}
                  >
                    <Text style={styles.chatButtonText}>Start Chatting</Text>
                    <ArrowRight size={20} color={theme.colors.onPrimary} />
                  </Pressable>
                </View>
              </FadeIn>
            </View>
          </View>

          {/* Mid-page banner ad */}
          {showAds && (
            <View style={styles.midPageAdContainer}>
              <BannerAdView size={BannerAdSize.MEDIUM_RECTANGLE} />
            </View>
          )}

          <View style={styles.featuresContainer}>
            <FadeIn delay={300}>
              <Text style={styles.featuresTitle}>Why Choose Us</Text>
              <View style={styles.featuresGrid}>
                <View style={styles.featureCard}>
                  <View style={styles.featureIconContainer}>
                    <Heart size={28} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureTitle}>Health Guidance</Text>
                  <Text style={styles.featureDescription}>
                    Get reliable information about symptoms, conditions, and general health topics.
                  </Text>
                </View>
                <View style={styles.featureCard}>
                  <View style={styles.featureIconContainer}>
                    <Stethoscope size={28} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureTitle}>24/7 Availability</Text>
                  <Text style={styles.featureDescription}>
                    Our AI assistant is always available to answer your health questions any time of day.
                  </Text>
                </View>
                <View style={styles.featureCard}>
                  <View style={styles.featureIconContainer}>
                    <Activity size={28} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureTitle}>Health Tracking</Text>
                  <Text style={styles.featureDescription}>
                    Monitor your health metrics and get personalized insights to improve your wellbeing.
                  </Text>
                </View>
              </View>
            </FadeIn>
          </View>

          <View style={styles.footer}>
            <Text style={styles.disclaimer}>
              Medical Advisor provides general information and is not a substitute for professional medical advice,
              diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider.
            </Text>
            <View style={styles.links}>
              <Text style={styles.link}>Privacy Policy</Text>
              <Text style={styles.link}>Terms of Service</Text>
              <Text style={styles.link}>Contact Us</Text>
            </View>
          </View>
        </ScrollView>

        {/* Bottom banner ad */}
        {showAds && <BannerAdView position="bottom" />}
      </View>
    </SafeAreaView>
  )
}

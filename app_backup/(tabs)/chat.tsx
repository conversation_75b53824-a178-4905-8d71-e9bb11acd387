"use client"

import { useState, useEffect, useRef } from "react"
import { View, StyleSheet, FlatList, KeyboardAvoidingView, Platform } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { StatusBar } from "expo-status-bar"
import { useTheme } from "@/hooks/useTheme"
import ChatBubble from "@/components/chat/ChatBubble"
import MessageInput from "@/components/chat/MessageInput"
import { useChatMessages } from "@/hooks/useChatMessages"
import { useChatHistory } from "@/hooks/useChatHistory"
import { useAdManager } from "@/hooks/useAdManager"
import BannerAdView from "@/components/ads/BannerAdView"
import TypingIndicator from "@/components/chat/TypingIndicator"
import EmptyState from "@/components/common/EmptyState"

export default function ChatScreen() {
  const theme = useTheme()
  const flatListRef = useRef(null)
  const { messages, sendMessage, isLoading } = useChatMessages()
  const { addChat } = useChatHistory()
  const { showAds } = useAdManager()
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)

  useEffect(() => {
    if (messages.length > 0 && !isLoading) {
      // Save the chat to history when a new message is added
      const lastMessage = messages[messages.length - 1]
      if (lastMessage.role === "assistant") {
        addChat({
          id: Date.now().toString(),
          title: messages[0]?.content.substring(0, 50) + "...",
          preview: lastMessage.content.substring(0, 50) + "...",
          timestamp: new Date().toISOString(),
          messages: [...messages],
        })
      }
    }
  }, [messages, isLoading])

  const handleSendMessage = async (message) => {
    await sendMessage(message)
    scrollToBottom()
  }

  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true })
    }
  }

  const handleScroll = (event) => {
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent
    const paddingToBottom = 20
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom

    setShowScrollToBottom(!isCloseToBottom)
  }

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
      paddingBottom: showAds ? 50 : 0,
    },
    chatContainer: {
      flex: 1,
      paddingHorizontal: 16,
    },
    messagesList: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 24,
    },
    scrollToBottomButton: {
      position: "absolute",
      right: 16,
      bottom: 80,
      backgroundColor: theme.colors.primaryContainer,
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: "center",
      alignItems: "center",
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
  })

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={theme.dark ? "light" : "dark"} />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
      >
        <View style={styles.chatContainer}>
          {messages.length === 0 ? (
            <EmptyState
              title="Start a Conversation"
              description="Ask me anything about health, symptoms, or medical information. I'm here to help!"
              image={require("@/assets/images/medical-chat-empty.jpg")}
              action={{
                label: "Start Chatting",
                onPress: () => handleSendMessage("Hello, I have a health question."),
              }}
            />
          ) : (
            <FlatList
              ref={flatListRef}
              data={messages}
              keyExtractor={(item, index) => `message-${index}`}
              renderItem={({ item }) => <ChatBubble message={item} />}
              contentContainerStyle={{ paddingTop: 16, paddingBottom: 16 }}
              onLayout={scrollToBottom}
              onScroll={handleScroll}
              scrollEventThrottle={400}
            />
          )}
          {isLoading && <TypingIndicator />}
        </View>
        <MessageInput onSendMessage={handleSendMessage} isLoading={isLoading} />
        {showAds && <BannerAdView position="bottom" />}
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

"use client"
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from "react-native"
import { useTheme } from "@rneui/themed"
import { MaterialIcons } from "@expo/vector-icons"

const History = () => {
  const { theme } = useTheme()

  const chatHistory = [
    { id: "1", title: "Project Brainstorm", lastMessage: "Okay, sounds good!", timestamp: "Yesterday" },
    { id: "2", title: "Quick Catch-up", lastMessage: "See you tomorrow.", timestamp: "2 days ago" },
    { id: "3", title: "Design Feedback", lastMessage: "Thanks for the feedback!", timestamp: "1 week ago" },
  ]

  const renderItem = ({ item }) => (
    <TouchableOpacity style={[styles.chatCard, { backgroundColor: theme.colors.background }]}>
      <View style={styles.chatIconContainer}>
        <MaterialIcons name="chat" size={28} color={theme.colors.primary} />
      </View>
      <View style={styles.chatContent}>
        <Text style={[styles.chatTitle, { color: theme.colors.text }]}>{item.title}</Text>
        <Text style={[styles.chatLastMessage, { color: theme.colors.secondary }]}>{item.lastMessage}</Text>
      </View>
      <Text style={[styles.chatTimestamp, { color: theme.colors.secondary }]}>{item.timestamp}</Text>
    </TouchableOpacity>
  )

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={chatHistory}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  listContent: {
    paddingBottom: 16,
  },
  chatCard: {
    borderRadius: 16,
    marginBottom: 16,
    padding: 18,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  chatIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#BBDEFB",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  chatContent: {
    flex: 1,
  },
  chatTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  chatLastMessage: {
    fontSize: 14,
  },
  chatTimestamp: {
    fontSize: 12,
    color: "gray",
  },
})

export default History

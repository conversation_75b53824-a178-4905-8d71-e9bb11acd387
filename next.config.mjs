/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config, { isServer, dev }) => {
    // Handle React Native Web
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      'react-native$': 'react-native-web',
      'react-native-web/dist/exports/I18nManager': 'react-native-web/dist/exports/I18nManager',
      'react-native-web/dist/exports/Platform': 'react-native-web/dist/exports/Platform',
      'react-native-web/dist/exports/View': 'react-native-web/dist/exports/View',
      'react-native-web/dist/exports/Text': 'react-native-web/dist/exports/Text',
    };

    config.resolve.extensions = [
      '.web.js',
      '.web.jsx',
      '.web.ts',
      '.web.tsx',
      ...config.resolve.extensions,
    ];

    // Handle React Native modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    return config;
  },
  transpilePackages: [
    'react-native',
    'react-native-web',
    'react-native-reanimated',
    'react-native-gesture-handler',
    'react-native-safe-area-context',
    'react-native-screens',
    'react-native-svg',
    'react-native-vector-icons',
    '@react-native/virtualized-lists',
    '@react-navigation/drawer',
    '@rneui/base',
    '@rneui/themed',
    'expo',
    'expo-constants',
    'expo-font',
    'expo-linear-gradient',
    'expo-linking',
    'expo-web-browser',
  ],
}

export default nextConfig

import { jest } from '@jest/globals';

describe('supabase client', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it('creates client with env variables', () => {
    const createClient = jest.fn(() => ({}));
    jest.doMock('expo-constants', () => ({
      expoConfig: { extra: { supabaseUrl: 'u', supabaseAnonKey: 'k' } },
    }));
    jest.doMock('@supabase/supabase-js', () => ({ createClient }));
    const { supabase } = require('../supabase');
    expect(supabase).toEqual({});
    expect(createClient).toHaveBeenCalledWith(
      'u',
      'k',
      expect.objectContaining({ auth: expect.any(Object) })
    );
  });

  it('throws when env vars missing', () => {
    jest.doMock('expo-constants', () => ({ expoConfig: { extra: {} } }));
    jest.doMock('@supabase/supabase-js', () => ({ createClient: jest.fn() }));
    expect(() => require('../supabase')).toThrow(
      'Missing Supabase environment variables'
    );
  });
});

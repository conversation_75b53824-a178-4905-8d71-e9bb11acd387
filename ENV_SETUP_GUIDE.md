# Medical Advisor App - Environment Setup Guide

## 🚀 Quick Start

1. **Copy the example environment file:**
   \`\`\`bash
   cp .env.example .env
   \`\`\`

2. **For local development, you can use the test values:**
   \`\`\`bash
   cp .env.local .env
   \`\`\`

## 📋 Required Services Setup

### 1. Supabase Setup

1. **Create a Supabase Project:**
   - Go to [https://app.supabase.com](https://app.supabase.com)
   - Click "New Project"
   - Fill in project details
   - Wait for project to be created

2. **Get your API Keys:**
   - Navigate to Settings → API
   - Copy the `Project URL` → `EXPO_PUBLIC_SUPABASE_URL`
   - Copy the `anon public` key → `EXPO_PUBLIC_SUPABASE_ANON_KEY`
   - Copy the `service_role` key → `SUPABASE_SERVICE_ROLE_KEY` (keep secret!)

3. **Run Database Migrations:**
   \`\`\`bash
   supabase link --project-ref YOUR_PROJECT_REF
   supabase db push
   \`\`\`

### 2. Stripe Setup

1. **Create a Stripe Account:**
   - Sign up at [https://stripe.com](https://stripe.com)
   - Complete account verification

2. **Get API Keys:**
   - Go to [Developers → API keys](https://dashboard.stripe.com/apikeys)
   - Copy `Publishable key` → `EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY`
   - Copy `Secret key` → `STRIPE_SECRET_KEY`

3. **Create Products and Prices:**
   - Go to [Products](https://dashboard.stripe.com/products)
   - Create subscription products (Monthly, Yearly, Lifetime)
   - Copy the price IDs to your .env file

4. **Setup Webhook:**
   - Go to [Webhooks](https://dashboard.stripe.com/webhooks)
   - Add endpoint: `https://your-domain.com/api/stripe-webhook`
   - Select events: `checkout.session.completed`, `customer.subscription.*`
   - Copy signing secret → `STRIPE_WEBHOOK_SECRET`

### 3. AdMob Setup

1. **Create AdMob Account:**
   - Sign up at [https://apps.admob.com](https://apps.admob.com)
   - Link to your Google Play / App Store accounts

2. **Add Your Apps:**
   - Click "Add App"
   - Create separate apps for iOS and Android
   - Copy App IDs to .env

3. **Create Ad Units:**
   For each platform (iOS/Android), create:
   - Banner Ad
   - Interstitial Ad
   - Rewarded Ad
   
   Copy all Ad Unit IDs to your .env file

4. **Add app-ads.txt:**
   - Create `app-ads.txt` file on your domain
   - Add your publisher ID

### 4. Mobile App Configuration

1. **Update app.json:**
   \`\`\`json
   {
     "expo": {
       "ios": {
         "bundleIdentifier": "com.yourcompany.medicaladvisor",
         "config": {
           "googleMobileAdsAppId": "YOUR_IOS_ADMOB_APP_ID"
         }
       },
       "android": {
         "package": "com.yourcompany.medicaladvisor",
         "config": {
           "googleMobileAdsAppId": "YOUR_ANDROID_ADMOB_APP_ID"
         }
       }
     }
   }
   \`\`\`

2. **Configure EAS Build:**
   \`\`\`bash
   eas build:configure
   \`\`\`

### 5. OpenAI Setup (Optional)

1. **Get API Key:**
   - Sign up at [https://platform.openai.com](https://platform.openai.com)
   - Go to [API keys](https://platform.openai.com/api-keys)
   - Create new secret key → `OPENAI_API_KEY`

2. **Set Usage Limits:**
   - Go to [Usage limits](https://platform.openai.com/account/limits)
   - Set monthly budget to prevent overcharges

### 6. iOS App Store Setup

1. **Apple Developer Account:**
   - Sign up at [https://developer.apple.com](https://developer.apple.com)
   - Pay the annual developer fee ($99/year)
   - Complete account verification

2. **App Store Connect Setup:**
   - Go to [App Store Connect](https://appstoreconnect.apple.com)
   - Click "My Apps" → "+" → "New App"
   - Fill in app information:
     - Platform: iOS
     - Name: Medical Advisor (or your app name)
     - Primary language
     - Bundle ID (must match `EXPO_PUBLIC_IOS_BUNDLE_IDENTIFIER`)
     - SKU (unique identifier for your app)

3. **Certificates & Provisioning:**
   - Go to [Certificates, Identifiers & Profiles](https://developer.apple.com/account/resources/certificates/list)
   - Create App ID (must match bundle identifier)
   - Create Distribution Certificate:
     \`\`\`bash
     # Generate certificate request
     openssl req -newkey rsa:2048 -nodes -keyout key.key -out request.csr
     # Upload request.csr to Apple Developer portal
     \`\`\`
   - Create Provisioning Profile (Distribution > App Store)
   - Download and save as `EXPO_APPLE_PROVISIONING_PROFILE_PATH`

4. **App-Specific Password:**
   - Go to [Apple ID Account Page](https://appleid.apple.com)
   - Security → App-Specific Passwords → Generate
   - Save as `EXPO_APPLE_PASSWORD`

5. **TestFlight Configuration:**
   - In App Store Connect → Your App → TestFlight
   - Add internal testers (Apple ID emails)
   - Create External Testing group (requires app review)
   - Set up build versioning in app.json:
     \`\`\`json
     "ios": {
       "buildNumber": "1.0.0"
     }
     \`\`\`

6. **App Store Submission Preparation:**
   - Create App Store screenshots (6.5", 5.5", 12.9" iPad)
   - Prepare app icon (1024x1024px PNG)
   - Write app description, keywords, support URL
   - Prepare privacy policy URL
   - Complete App Privacy information

7. **EAS Build & Submit:**
   \`\`\`bash
   # Build for iOS
   eas build --platform ios --profile production
   
   # Submit to App Store
   eas submit --platform ios
   \`\`\`

### 7. Google Play Store Setup

1. **Google Play Developer Account:**
   - Sign up at [https://play.google.com/console](https://play.google.com/console)
   - Pay one-time registration fee ($25)
   - Complete account verification

2. **Create Application:**
   - Go to "All apps" → "Create app"
   - Enter app name, default language
   - Specify "App or Game", "Free or Paid"
   - Confirm developer program agreement

3. **App Signing Setup:**
   - Generate upload keystore:
     \`\`\`bash
     keytool -genkeypair -v -storetype PKCS12 -keystore upload-keystore.p12 -alias upload-key -keyalg RSA -keysize 2048 -validity 10000
     \`\`\`
   - Save keystore password as `EXPO_ANDROID_KEYSTORE_PASSWORD`
   - Save key alias password as `EXPO_ANDROID_KEY_PASSWORD`
   - Save keystore file path as `EXPO_ANDROID_KEYSTORE_PATH`
   - Set in app.json:
     \`\`\`json
     "android": {
       "package": "com.yourcompany.medicaladvisor",
       "versionCode": 1
     }
     \`\`\`

4. **Play Store Listing:**
   - Prepare short description (80 chars max)
   - Prepare full description (4000 chars max)
   - Create feature graphic (1024x500px)
   - Create app icon (512x512px)
   - Create screenshots (phone, 7" tablet, 10" tablet)
   - Prepare promo video (YouTube URL)

5. **Content Rating:**
   - Complete the rating questionnaire
   - Specify target audience age range
   - Confirm if app appeals to children

6. **Pricing & Distribution:**
   - Select countries for distribution
   - Set up pricing (if applicable)
   - Confirm content guidelines compliance
   - Set up app category and tags

7. **Release Tracks:**
   - Internal testing (instant, limited to Google Groups)
   - Closed testing (faster review, limited audience)
   - Open testing (public beta)
   - Production (full release)

8. **EAS Build & Submit:**
   \`\`\`bash
   # Build for Android
   eas build --platform android --profile production
   
   # Submit to Play Store
   eas submit --platform android
   \`\`\`

9. **Play Console API (Optional):**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create project and enable Play Developer API
   - Create service account with Play Console access
   - Download JSON key file
   - Save path as `GOOGLE_PLAY_SERVICE_ACCOUNT_KEY_PATH`

### 8. App Configuration

1. **Update app.json with Store Information:**
   \`\`\`json
   {
     "expo": {
       "name": "Medical Advisor",
       "slug": "medical-advisor",
       "version": "1.0.0",
       "orientation": "portrait",
       "icon": "./assets/icon.png",
       "userInterfaceStyle": "light",
       "splash": {
         "image": "./assets/splash.png",
         "resizeMode": "contain",
         "backgroundColor": "#ffffff"
       },
       "assetBundlePatterns": ["**/*"],
       "ios": {
         "supportsTablet": true,
         "bundleIdentifier": "com.yourcompany.medicaladvisor",
         "buildNumber": "1.0.0",
         "config": {
           "googleMobileAdsAppId": "YOUR_IOS_ADMOB_APP_ID"
         },
         "infoPlist": {
           "NSCameraUsageDescription": "This app uses the camera to scan health documents.",
           "NSPhotoLibraryUsageDescription": "This app uses photos to upload health documents."
         }
       },
       "android": {
         "adaptiveIcon": {
           "foregroundImage": "./assets/adaptive-icon.png",
           "backgroundColor": "#ffffff"
         },
         "package": "com.yourcompany.medicaladvisor",
         "versionCode": 1,
         "config": {
           "googleMobileAdsAppId": "YOUR_ANDROID_ADMOB_APP_ID"
         },
         "permissions": [
           "CAMERA",
           "READ_EXTERNAL_STORAGE",
           "WRITE_EXTERNAL_STORAGE"
         ]
       },
       "extra": {
         "eas": {
           "projectId": "YOUR_EAS_PROJECT_ID"
         }
       }
     }
   }
   \`\`\`

2. **Configure eas.json:**
   \`\`\`json
   {
     "cli": {
       "version": ">= 3.13.3"
     },
     "build": {
       "development": {
         "developmentClient": true,
         "distribution": "internal"
       },
       "preview": {
         "distribution": "internal"
       },
       "production": {
         "autoIncrement": true
       }
     },
     "submit": {
       "production": {
         "ios": {
           "appleId": "<EMAIL>",
           "ascAppId": "YOUR_APP_STORE_CONNECT_APP_ID",
           "appleTeamId": "YOUR_APPLE_TEAM_ID"
         },
         "android": {
           "track": "production"
         }
       }
     }
   }
   \`\`\`

## 🔒 Security Best Practices

1. **Never commit .env files to Git**
   - Ensure `.env` is in `.gitignore`
   - Use `.env.example` for documentation

2. **Use different keys for environments:**
   - Development: Test keys
   - Staging: Separate keys
   - Production: Production keys

3. **Rotate keys regularly:**
   - Set reminders to rotate API keys
   - Update webhook secrets periodically

4. **Limit key permissions:**
   - Use restricted API keys where possible
   - Enable only required permissions

## 🚀 Deployment

### Vercel Deployment

1. **Add Environment Variables:**
   - Go to Project Settings → Environment Variables
   - Add all variables from .env
   - Use different values for Preview/Production

2. **Configure Secrets:**
   \`\`\`bash
   vercel env add STRIPE_SECRET_KEY
   vercel env add SUPABASE_SERVICE_ROLE_KEY
   # Add other sensitive variables
   \`\`\`

### EAS Build

1. **Configure Secrets:**
   \`\`\`bash
   eas secret:create --name EXPO_PUBLIC_SUPABASE_URL --value "your-value"
   # Repeat for all secrets
   \`\`\`

2. **Build for Production:**
   \`\`\`bash
   eas build --platform all --profile production
   \`\`\`

## 🧪 Testing

Use these test values during development:

- **Stripe Test Cards:**
  - Success: `4242 4242 4242 4242`
  - Decline: `4000 0000 0000 0002`

- **AdMob Test IDs:**
  - Already included in `.env.local`

### Running Jest Tests

Install dependencies and run tests with:
```bash
pnpm install
pnpm test
```

The Jest setup file `jest.setup.js` ensures React 19's `act` works correctly.

## 📞 Support

If you need help with setup:
1. Check service documentation
2. Review error logs
3. Contact support for each service

## ✅ Setup Checklist

- [ ] Copied .env.example to .env
- [ ] Set up Supabase project and keys
- [ ] Configured Stripe products and webhook
- [ ] Created AdMob apps and ad units
- [ ] Updated app.json with IDs
- [ ] Configured OpenAI (if using AI features)
- [ ] Set up Apple Developer account and certificates
- [ ] Created App Store Connect listing
- [ ] Generated iOS provisioning profiles
- [ ] Set up Google Play Developer account
- [ ] Created Android keystore and signing config
- [ ] Prepared store listings and screenshots
- [ ] Added environment variables to deployment platform
- [ ] Tested with development keys
- [ ] Secured production keys
- [ ] Configured app.json and eas.json
- [ ] Set up CI/CD for automated builds

import { mockStreamMessage } from '../api';

jest.useFakeTimers();

describe.skip('mockStreamMessage', () => {
  it('streams chunks and completes', async () => {
    const onChunk = jest.fn();
    const onComplete = jest.fn();
    const onError = jest.fn();

    const promise = mockStreamMessage('headache', onChunk, onComplete, onError);
    jest.runAllTimers();
    await promise;

    expect(onChunk).toHaveBeenCalled();
    expect(onComplete).toHaveBeenCalled();
    expect(onError).not.toHaveBeenCalled();
  });
});

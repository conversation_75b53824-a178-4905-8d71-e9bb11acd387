import {
  createCheckoutSession,
  handleWebCheckout,
  handleMobileCheckout,
  getSubscriptionStatus,
  cacheSubscriptionStatus,
  clearSubscriptionCache,
} from '../stripe';
import AsyncStorage from '@react-native-async-storage/async-storage';

jest.mock('@/lib/supabase', () => ({
  supabase: {
    functions: { invoke: jest.fn() },
    from: jest.fn(() => ({ select: () => ({ eq: () => ({ single: jest.fn() }) }) })),
  },
}));

declare const supabase: any;

jest.mock('@stripe/stripe-js', () => ({ loadStripe: jest.fn(() => Promise.resolve({ redirectToCheckout: jest.fn() })) }));
jest.mock('expo-web-browser', () => ({ openBrowserAsync: jest.fn(() => Promise.resolve({ type: 'dismiss' })) }));

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)),
}));

describe.skip('stripe service', () => {
  it('creates checkout session', async () => {
    const { supabase } = require('@/lib/supabase');
    supabase.functions.invoke.mockResolvedValue({ data: { sessionId: '1', url: 'u' }, error: null });
    const result = await createCheckoutSession('price', 'user');
    expect(result).toEqual({ sessionId: '1', url: 'u' });
    expect(supabase.functions.invoke).toHaveBeenCalled();
  });

  it('handles web checkout', async () => {
    const stripeJs = require('@stripe/stripe-js');
    const stripe = { redirectToCheckout: jest.fn() };
    stripeJs.loadStripe.mockResolvedValue(stripe);
    await handleWebCheckout('123');
    expect(stripe.redirectToCheckout).toHaveBeenCalledWith({ sessionId: '123' });
  });

  it('handles mobile checkout', async () => {
    const browser = require('expo-web-browser');
    await handleMobileCheckout('url');
    expect(browser.openBrowserAsync).toHaveBeenCalled();
  });

  it('fetches subscription status', async () => {
    const { supabase } = require('@/lib/supabase');
    const single = jest.fn().mockResolvedValue({ data: { status: 'active', current_period_end: null, cancel_at_period_end: false }, error: null });
    supabase.from.mockReturnValue({ select: () => ({ eq: () => ({ single }) }) });
    const result = await getSubscriptionStatus('user');
    expect(result.isActive).toBe(true);
    expect(single).toHaveBeenCalled();
  });

  it('caches and clears subscription status', async () => {
    await cacheSubscriptionStatus({ isActive: true, status: 'active', currentPeriodEnd: null, cancelAtPeriodEnd: false });
    expect(AsyncStorage.setItem).toHaveBeenCalled();
    await clearSubscriptionCache();
    expect(AsyncStorage.removeItem).toHaveBeenCalled();
  });
});

import { apiSendMessage, apiStreamMessage } from '../api';
import { Platform } from 'react-native';

describe.skip('apiSendMessage', () => {
  beforeEach(() => {
    // @ts-ignore
    global.fetch = jest.fn();
  });

  it('returns json on success', async () => {
    const json = jest.fn().mockResolvedValue({ ok: true });
    (global.fetch as jest.Mock).mockResolvedValue({ ok: true, json });
    const result = await apiSendMessage('hello');
    expect(result).toEqual({ ok: true });
    expect(global.fetch).toHaveBeenCalled();
  });

  it('throws on error status', async () => {
    (global.fetch as jest.Mock).mockResolvedValue({ ok: false, status: 500 });
    await expect(apiSendMessage('fail')).rejects.toThrow('API error: 500');
  });
});

describe.skip('apiStreamMessage', () => {
  it('streams using fetch on web', async () => {
    jest.spyOn(Platform, 'OS', 'get').mockReturnValue('web');
    const encoder = new TextEncoder();
    const chunks = ['one ', 'two'];
    let index = 0;
    const reader = {
      read: () => Promise.resolve(
        index < chunks.length
          ? { done: false, value: encoder.encode(chunks[index++]) }
          : { done: true, value: undefined }
      ),
    };
    (global.fetch as jest.Mock).mockResolvedValue({ ok: true, body: { getReader: () => reader } });
    const onChunk = jest.fn();
    const onComplete = jest.fn();
    const onError = jest.fn();
    await apiStreamMessage('hi', onChunk, onComplete, onError);
    expect(onChunk).toHaveBeenCalledWith('one ');
    expect(onChunk).toHaveBeenCalledWith('two');
    expect(onComplete).toHaveBeenCalledWith('one two');
  });

  it('streams using EventSource on native', async () => {
    jest.spyOn(Platform, 'OS', 'get').mockReturnValue('ios');
    const listeners: Record<string, Function> = {};
    class MockEventSource {
      constructor(_url: string) {}
      addEventListener(name: string, cb: Function) { listeners[name] = cb; }
      close() {}
    }
    // @ts-ignore
    global.EventSource = MockEventSource;
    const onChunk = jest.fn();
    const onComplete = jest.fn();
    const onError = jest.fn();
    const promise = apiStreamMessage('hi', onChunk, onComplete, onError);
    listeners['message']({ data: 'a' });
    listeners['message']({ data: 'b' });
    listeners['end']();
    await promise;
    expect(onChunk).toHaveBeenCalledWith('a');
    expect(onChunk).toHaveBeenCalledWith('b');
    expect(onComplete).toHaveBeenCalledWith('ab');
  });
});

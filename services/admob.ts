import { Platform } from "react-native"
import AsyncStorage from "@react-native-async-storage/async-storage"

// Web-safe fallbacks for all platforms during development
const BannerAdSize = {
  BANNER: 'BANNER',
  LARGE_BANNER: 'LARGE_BANNER',
  MEDIUM_RECTANGLE: 'MEDIUM_RECTANGLE',
  FULL_BANNER: 'FULL_BANNER',
  LEADERBOARD: 'LEADERBOARD',
  ADAPTIVE_BANNER: 'ADAPTIVE_BANNER',
}

const TestIds = {
  BANNER: 'web-test-banner',
  INTERSTITIAL: 'web-test-interstitial',
  REWARDED: 'web-test-rewarded',
}

const AdEventType = {
  LOADED: 'loaded',
  CLOSED: 'closed',
}

declare const __DEV__: boolean

// Replace with your actual ad unit IDs in production
const AD_UNITS = {
  banner: {
    ios: __DEV__ ? TestIds.BANNER : "ca-app-pub-XXXXXXXXXXXXXXXX/YYYYYYYYYY",
    android: __DEV__ ? TestIds.BANNER : "ca-app-pub-XXXXXXXXXXXXXXXX/ZZZZZZZZZZ",
  },
  interstitial: {
    ios: __DEV__ ? TestIds.INTERSTITIAL : "ca-app-pub-XXXXXXXXXXXXXXXX/YYYYYYYYYY",
    android: __DEV__ ? TestIds.INTERSTITIAL : "ca-app-pub-XXXXXXXXXXXXXXXX/ZZZZZZZZZZ",
  },
  rewarded: {
    ios: __DEV__ ? TestIds.REWARDED : "ca-app-pub-XXXXXXXXXXXXXXXX/YYYYYYYYYY",
    android: __DEV__ ? TestIds.REWARDED : "ca-app-pub-XXXXXXXXXXXXXXXX/ZZZZZZZZZZ",
  },
}

// Get the appropriate ad unit ID based on platform
const getAdUnitId = (adType: keyof typeof AD_UNITS) => {
  const platform = Platform.OS === "ios" ? "ios" : "android"
  return AD_UNITS[adType][platform]
}

// Interstitial ad management
class InterstitialAdManager {
  private interstitialAd: any = null
  private isLoaded = false
  private lastShownTimestamp = 0
  private readonly MIN_INTERVAL_BETWEEN_ADS = 60000 // 1 minute in milliseconds
  private unsubscribeFunctions: (() => void)[] = []

  constructor() {
    if (Platform.OS !== 'web') {
      this.loadInterstitialAd()
    } else {
      // On web, simulate loaded state
      this.isLoaded = true
    }
  }

  private loadInterstitialAd() {
    // TODO: Implement native AdMob for production
    console.log("loadInterstitialAd: Development mode - no real ads loaded")
    this.isLoaded = true
  }

  public showAdIfReady(force = false): Promise<boolean> {
    return new Promise(async (resolve) => {
      console.log("showAdIfReady: Development mode - simulating ad")

      // Check if user has premium subscription
      const isAdFree = await this.isUserAdFree()
      if (isAdFree) {
        console.log("User has premium subscription, skipping ad")
        resolve(false)
        return
      }

      // Check if enough time has passed since the last ad
      const now = Date.now()
      const timeSinceLastAd = now - this.lastShownTimestamp

      if (!force && timeSinceLastAd < this.MIN_INTERVAL_BETWEEN_ADS) {
        console.log("Not enough time has passed since last ad")
        resolve(false)
        return
      }

      // Simulate showing ad
      this.lastShownTimestamp = now
      resolve(false) // Don't block navigation in development
    })
  }

  private async isUserAdFree(): Promise<boolean> {
    try {
      const subscriptionData = await AsyncStorage.getItem("medical_advisor_subscription_cache")
      if (subscriptionData) {
        const { status } = JSON.parse(subscriptionData)
        return status.isActive
      }
      return false
    } catch (error) {
      console.error("Error checking subscription status:", error)
      return false
    }
  }

  public dispose() {
    // Clean up (development mode)
    console.log("dispose: Development mode - cleaning up")
  }
}

// Create a singleton instance
let interstitialAdManager: InterstitialAdManager | null = null

export const getInterstitialAdManager = () => {
  if (!interstitialAdManager) {
    interstitialAdManager = new InterstitialAdManager()
  }
  return interstitialAdManager
}

// Track ad impressions for analytics
export const trackAdImpression = async (adType: string) => {
  try {
    // Here you would typically send analytics data
    console.log(`Ad impression: ${adType}`)
  } catch (error) {
    console.error("Error tracking ad impression:", error)
  }
}

// Helper to determine if ads should be shown based on subscription status
export const shouldShowAds = async (): Promise<boolean> => {
  try {
    const subscriptionData = await AsyncStorage.getItem("medical_advisor_subscription_cache")
    if (subscriptionData) {
      const { status } = JSON.parse(subscriptionData)
      return !status.isActive
    }
    return true
  } catch (error) {
    console.error("Error checking if ads should be shown:", error)
    return true
  }
}

// Export banner ad unit ID for components
export const getBannerAdUnitId = () => getAdUnitId("banner")

// Export banner ad sizes (development mode - all platforms use same values)
export const BANNER_AD_SIZES = BannerAdSize

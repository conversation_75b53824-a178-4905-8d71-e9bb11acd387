"use client"

import { useState, useEffect } from "react"
import { View, StyleSheet, Text } from "react-native"
import { useSubscription } from "@/hooks/useSubscription"

interface BannerAdViewProps {
  size?: string
  position?: "top" | "bottom"
}

export default function BannerAdView({ size = 'ADAPTIVE_BANNER', position = "bottom" }: BannerAdViewProps) {
  const { subscription } = useSubscription()
  const [isAdFree, setIsAdFree] = useState(false)

  useEffect(() => {
    // Check if user has premium subscription
    setIsAdFree(subscription.isActive)
  }, [subscription])

  // Don't render the ad for premium users
  if (isAdFree) {
    return null
  }

  const styles = StyleSheet.create({
    container: {
      width: "100%",
      backgroundColor: "#f0f0f0",
      alignItems: "center",
      justifyContent: "center",
      height: size === 'MEDIUM_RECTANGLE' ? 250 : 60,
      borderRadius: 8,
      margin: 8,
    },
    text: {
      textAlign: 'center',
      color: '#666',
      fontSize: 12,
    }
  })

  return (
    <View style={styles.container}>
      <Text style={styles.text}>
        Ad Space (Mobile Only)
      </Text>
      <Text style={[styles.text, { fontSize: 10, marginTop: 4 }]}>
        {size}
      </Text>
    </View>
  )
}

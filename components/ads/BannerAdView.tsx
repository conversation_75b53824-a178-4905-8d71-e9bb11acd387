"use client"

import { useState, useEffect } from "react"
import { View, StyleSheet, Platform, Text } from "react-native"
import { useSubscription } from "@/hooks/useSubscription"

interface BannerAdViewProps {
  size?: string
  position?: "top" | "bottom"
}

export default function BannerAdView({ size = 'ADAPTIVE_BANNER', position = "bottom" }: BannerAdViewProps) {
  const { subscription } = useSubscription()
  const [isAdFree, setIsAdFree] = useState(false)

  useEffect(() => {
    // Check if user has premium subscription
    setIsAdFree(subscription.isActive)
  }, [subscription])

  // Don't render the ad for premium users
  if (isAdFree) {
    return null
  }

  const getAdHeight = () => {
    switch (size) {
      case 'MEDIUM_RECTANGLE':
        return 250
      case 'LARGE_BANNER':
        return 100
      default:
        return 60
    }
  }

  const styles = StyleSheet.create({
    container: {
      width: "100%",
      backgroundColor: "#f0f0f0",
      alignItems: "center",
      justifyContent: "center",
      height: getAdHeight(),
      borderRadius: 8,
      margin: 8,
    },
    text: {
      textAlign: 'center',
      color: '#666',
      fontSize: 12,
    }
  })

  // For now, show placeholder on all platforms
  // TODO: Implement native AdMob for mobile platforms
  return (
    <View style={styles.container}>
      <Text style={styles.text}>
        {Platform.OS === 'web' ? 'Ad Space (Mobile Only)' : 'Ad Loading...'}
      </Text>
      <Text style={[styles.text, { fontSize: 10, marginTop: 4 }]}>
        {size}
      </Text>
    </View>
  )
}

"use client"

import type React from "react"
import { useState, useCallback } from "react"
import { View, TextInput, TouchableOpacity, StyleSheet, Platform } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "@/hooks/useTheme"

interface MessageInputProps {
  onSend: (message: string) => void
  isStreaming: boolean
}

const MessageInput: React.FC<MessageInputProps> = ({ onSend, isStreaming }) => {
  const [message, setMessage] = useState("")
  const theme = useTheme()

  const handleSend = useCallback(() => {
    if (message.trim()) {
      onSend(message)
      setMessage("")
    }
  }, [message, onSend])

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        placeholder="Type a message..."
        placeholderTextColor={theme.colors.secondary}
        value={message}
        onChangeText={setMessage}
        multiline
        textAlignVertical="top"
      />
      <TouchableOpacity style={styles.sendButton} onPress={handleSend} disabled={isStreaming || !message.trim()}>
        <Feather
          name="send"
          size={24}
          color={message.trim() && !isStreaming ? theme.colors.onPrimary : theme.colors.secondary}
        />
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingBottom: 24,
    paddingTop: 8,
    backgroundColor: "transparent",
  },
  input: {
    flex: 1,
    backgroundColor: "white",
    borderRadius: 28,
    paddingHorizontal: 20,
    paddingVertical: Platform.OS === "ios" ? 14 : 10,
    fontFamily: "Roboto-Regular",
    fontSize: 16,
    color: "black",
    maxHeight: 120,
    borderWidth: 2,
    borderColor: "lightgray",
    elevation: 1,
    shadowColor: "black",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sendButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: "lightgray",
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 12,
    elevation: 3,
    shadowColor: "black",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
})

export default MessageInput

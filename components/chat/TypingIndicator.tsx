"use client"

import { useEffect } from "react"
import { View, StyleSheet } from "react-native"
import { useTheme } from "@/hooks/useTheme"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  withSequence,
  Easing,
} from "react-native-reanimated"

export default function TypingIndicator() {
  const theme = useTheme()

  const dots = [useSharedValue(0), useSharedValue(0), useSharedValue(0)]

  useEffect(() => {
    dots.forEach((dot, index) => {
      dot.value = withRepeat(
        withSequence(
          withDelay(index * 200, withTiming(1, { duration: 400, easing: Easing.ease })),
          withDelay(600, withTiming(0, { duration: 400, easing: Easing.ease })),
        ),
        -1,
        false,
      )
    })
  }, [])

  const dotAnimatedStyles = dots.map((dot) =>
    useAnimatedStyle(() => {
      return {
        opacity: 0.4 + dot.value * 0.6,
        transform: [{ scale: 0.8 + dot.value * 0.2 }],
      }
    }),
  )

  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      padding: 16,
      borderRadius: 20,
      backgroundColor: theme.colors.surfaceContainerHigh,
      alignSelf: "flex-start",
      marginVertical: 8,
      borderBottomLeftRadius: 4,
      elevation: 1,
      boxShadow: `0px 1px 1px ${theme.colors.shadow}10`,
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.primary,
      margin: 2,
    },
  })

  return (
    <View style={styles.container}>
      {dots.map((_, index) => (
        <Animated.View key={index} style={[styles.dot, dotAnimatedStyles[index]]} />
      ))}
    </View>
  )
}

"use client"

import type React from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTheme } from "@/hooks/useTheme"
import { Message } from "@/types"

interface ChatBubbleProps {
  message: Message
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message }) => {
  const theme = useTheme()
  const isUser = message.role === 'user'

  const styles = StyleSheet.create({
    bubbleContainer: {
      borderRadius: 24,
      padding: 16,
      backgroundColor: isUser ? theme.colors.primary : theme.colors.surface,
      borderBottomLeftRadius: isUser ? 24 : 8,
      borderBottomRightRadius: isUser ? 8 : 24,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      borderWidth: isUser ? 0 : 1,
      borderColor: isUser ? "transparent" : theme.colors.outlineVariant,
    },
    text: {
      fontSize: 16,
      color: isUser ? theme.colors.onPrimary : theme.colors.onSurface,
    },
  })

  return (
    <View style={[styles.bubbleContainer, { alignSelf: isUser ? "flex-end" : "flex-start" }]}>
      <Text style={styles.text}>{message.content}</Text>
    </View>
  )
}

export default ChatBubble

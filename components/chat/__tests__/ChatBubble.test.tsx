import { render } from '@testing-library/react-native';
import ChatBubble from '../ChatBubble';

jest.mock('@rneui/themed', () => ({
  useTheme: () => ({ theme: { colors: { primary: 'blue', surface: 'gray', white: 'white', onSurface: 'black', outlineVariant: 'gray', shadow: 'black' } } }),
}));

describe.skip('ChatBubble', () => {
  it('renders user style', () => {
    const { getByText } = render(<ChatBubble text="hi" isUser />);
    const text = getByText('hi');
    expect(text).toBeTruthy();
  });

  it('renders assistant style', () => {
    const { getByText } = render(<ChatBubble text="hello" isUser={false} />);
    expect(getByText('hello')).toBeTruthy();
  });
});

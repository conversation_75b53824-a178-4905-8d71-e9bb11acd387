"use client"

import { useState } from "react"
import { View, Text, StyleSheet, Pressable, ActivityIndicator, Platform, Image } from "react-native"
import { useTheme } from "@/hooks/useTheme"
import { supabase } from "@/lib/supabase"
import { Chrome as Google, Apple } from "lucide-react-native"
import FadeIn from "@/components/animations/FadeIn"

export default function AuthScreen() {
  const theme = useTheme();
  const [loading, setLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleOAuthLogin = async (provider: 'google' | 'apple') => {
    try {
      setLoading(provider);
      setError(null);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: Platform.select({
            web: window.location.origin,
            default: 'medical-advisor://',
          }),
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) throw error;
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(null);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      padding: 24,
      backgroundColor: theme.colors.background,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 40,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: 20,
      backgroundColor: theme.colors.primaryContainer,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    title: {
      fontSize: 28,
      fontFamily: 'Roboto-Bold',
      color: theme.colors.onBackground,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'Roboto-Regular',
      color: theme.colors.onSurfaceVariant,
      marginBottom: 40,
      textAlign: 'center',
      maxWidth: 300,
      alignSelf: 'center',
      lineHeight: 24,
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 28,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      elevation: 1,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    buttonText: {
      color: theme.colors.onSurface,
      fontSize: 16,
      fontFamily: 'Roboto-Medium',
      marginLeft: 12,
    },
    error: {
      color: theme.colors.error,
      fontSize: 14,
      fontFamily: 'Roboto-Regular',
      marginTop: 16,
      textAlign: 'center',
    },
    footer: {
      marginTop: 40,
      alignItems: 'center',
    },
    footerText: {
      fontSize: 14,
      fontFamily: 'Roboto-Regular',
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 8,
    },
    footerLink: {
      fontSize: 14,
      fontFamily: 'Roboto-Medium',
      color: theme.colors.primary,
      textDecorationLine: 'underline',
    },
  });

  return (
    <View style={styles.container}>
      <FadeIn>
        <View style={styles.logoContainer}>
          <View style={styles.logo}>
            <Image 
              source={require('@/assets/images/icon.png')}
              style={{ width: 60, height: 60 }}
              resizeMode="contain"
            />
          </View>
        </View>
        
        <Text style={styles.title}>Welcome to Medical Advisor</Text>
        <Text style={styles.subtitle}>
          Sign in with your preferred account to access personalized health guidance
        </Text>

        <Pressable 
          style={styles.button}
          onPress={() => handleOAuthLogin('google')}
          disabled={loading !== null}
          android_ripple={{ color: theme.colors.ripple }}
        >
          {loading === 'google' ? (
            <ActivityIndicator color={theme.colors.primary} />
          ) : (
            <>
              <Google size={24} color={theme.colors.onSurface} />
              <Text style={styles.buttonText}>Continue with Google</Text>
            </>
          )}
        </Pressable>

        {Platform.OS !== 'android' && (
          <Pressable 
            style={styles.button}
            onPress={() => handleOAuthLogin('apple')}
            disabled={loading !== null}
            android_ripple={{ color: theme.colors.ripple }}
          >
            {loading === 'apple' ? (
              <ActivityIndicator color={theme.colors.primary} />
            ) : (
              <>
                <Apple size={24} color={theme.colors.onSurface} />
                <Text style={styles.buttonText}>Continue with Apple</Text>
              </>
            )}
          </Pressable>
        )}

        {error && <Text style={styles.error}>{error}</Text>}
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By continuing, you agree to our
          </Text>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <Text style={styles.footerLink}>Terms of Service</Text>
            <Text style={styles.footerLink}>Privacy Policy</Text>
          </View>
        </View>
      </FadeIn>
    </View>
  );
}

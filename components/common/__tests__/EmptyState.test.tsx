import { render, fireEvent } from '@testing-library/react-native';
import React from 'react';
import EmptyState from '../EmptyState';

jest.mock('@/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primaryContainer: 'blue',
      primary: 'blue',
      onBackground: 'black',
      onSurfaceVariant: 'gray',
      onPrimary: 'white',
      ripple: 'rgba(0,0,0,0.1)',
    },
  }),
}));

jest.mock('@/components/animations/FadeIn', () => 'FadeIn');

describe.skip('EmptyState', () => {
  it('renders default icon when none provided', () => {
    const { getByText } = render(<EmptyState title="Title" description="Desc" />);
    expect(getByText('Title')).toBeTruthy();
    expect(getByText('Desc')).toBeTruthy();
  });

  it('renders action and handles press', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <EmptyState
        title="Title"
        description="Desc"
        action={{ label: 'Go', onPress }}
      />
    );
    const button = getByText('Go');
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalled();
  });

  it('renders custom icon', () => {
    const icon = <>{'ICON'}</>;
    const { getByText } = render(
      <EmptyState title="Title" description="Desc" icon={icon} />
    );
    expect(getByText('ICON')).toBeTruthy();
  });
});

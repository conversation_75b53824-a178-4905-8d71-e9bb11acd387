import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import { Message, ChatSession } from '@/types';

const STORAGE_KEY = 'medical_advisor_chat_history';

export function useChatHistory() {
  const [chatHistory, setChatHistory] = useState<ChatSession[]>([]);
  const [initialized, setInitialized] = useState(false);

  // Load chat history from storage on mount
  useEffect(() => {
    const loadChatHistory = async () => {
      try {
        const storedHistory = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedHistory) {
          setChatHistory(JSON.parse(storedHistory));
        }
        setInitialized(true);
      } catch (error) {
        console.error('Error loading chat history:', error);
        setInitialized(true);
      }
    };

    loadChatHistory();
  }, []);

  // Save chat history to storage whenever it changes
  useEffect(() => {
    const saveChatHistory = async () => {
      if (initialized) {
        try {
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(chatHistory));
        } catch (error) {
          console.error('Error saving chat history:', error);
        }
      }
    };

    saveChatHistory();
  }, [chatHistory, initialized]);

  // Create a new chat session
  const createChat = useCallback((messages: Message[] = []) => {
    const newChat: ChatSession = {
      id: uuidv4(),
      messages,
      lastUpdated: new Date().toISOString(),
      isArchived: false,
    };

    setChatHistory(prev => [newChat, ...prev]);
    return newChat.id;
  }, []);

  // Add a new chat session with custom properties
  const addChat = useCallback((chatData: Partial<ChatSession> & { messages: Message[] }) => {
    const newChat: ChatSession = {
      id: chatData.id || uuidv4(),
      title: chatData.title,
      preview: chatData.preview,
      messages: chatData.messages,
      timestamp: chatData.timestamp,
      lastUpdated: new Date().toISOString(),
      isArchived: false,
    };

    setChatHistory(prev => [newChat, ...prev]);
    return newChat.id;
  }, []);

  // Update an existing chat session
  const updateChat = useCallback((chatId: string, messages: Message[]) => {
    setChatHistory(prev => 
      prev.map(chat => 
        chat.id === chatId 
          ? { 
              ...chat, 
              messages, 
              lastUpdated: new Date().toISOString() 
            } 
          : chat
      )
    );
  }, []);

  // Delete a chat session
  const deleteChat = useCallback((chatId: string) => {
    setChatHistory(prev => prev.filter(chat => chat.id !== chatId));
  }, []);

  // Archive a chat session
  const archiveChat = useCallback((chatId: string) => {
    setChatHistory(prev => 
      prev.map(chat => 
        chat.id === chatId 
          ? { ...chat, isArchived: true } 
          : chat
      )
    );
  }, []);

  // Load a specific chat for viewing/continuing
  const loadChat = useCallback((chatId: string) => {
    const chat = chatHistory.find(c => c.id === chatId);
    if (chat) {
      // This would typically set the active chat in another context/state
      return chat.messages;
    }
    return [];
  }, [chatHistory]);

  return {
    chatHistory: chatHistory.filter(chat => !chat.isArchived),
    archivedChats: chatHistory.filter(chat => chat.isArchived),
    createChat,
    addChat,
    updateChat,
    deleteChat,
    archiveChat,
    loadChat,
  };
}

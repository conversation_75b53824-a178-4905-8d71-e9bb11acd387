import { renderHook, act } from '@testing-library/react';
import { useChatStorage } from '../useChatStorage';

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve('id1')),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
}));

const AsyncStorage = require('@react-native-async-storage/async-storage');

describe('useChatStorage', () => {
  it('gets active chat id', async () => {
    const { result } = renderHook(() => useChatStorage());
    const id = await result.current.getActiveChatId();
    expect(id).toBe('id1');
    expect(AsyncStorage.getItem).toHaveBeenCalled();
  });

  it('sets and clears active chat id', async () => {
    const { result } = renderHook(() => useChatStorage());

    await act(async () => {
      await result.current.setActiveChatId('id2');
      await result.current.clearActiveChatId();
    });

    expect(AsyncStorage.setItem).toHaveBeenCalledWith('medical_advisor_active_chat_id', 'id2');
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('medical_advisor_active_chat_id');
  });
});

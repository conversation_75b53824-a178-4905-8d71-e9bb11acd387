import { renderHook, act } from '@testing-library/react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useChatHistory } from '../useChatHistory';

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
}));

jest.mock('uuid', () => ({ v4: () => 'uuid-1' }));

describe('useChatHistory', () => {
  it('creates and updates chat with timestamp', async () => {
    jest.useFakeTimers().setSystemTime(new Date('2023-01-01T00:00:00Z'));
    const { result } = renderHook(() => useChatHistory());

    await act(async () => {
      const chatId = result.current.createChat([]);
      expect(chatId).toBe('uuid-1');
    });

    expect(result.current.chatHistory[0].lastUpdated).toBe(new Date('2023-01-01T00:00:00Z').toISOString());

    jest.setSystemTime(new Date('2023-01-01T00:01:00Z'));
    act(() => {
      result.current.updateChat('uuid-1', []);
    });

    expect(result.current.chatHistory[0].lastUpdated).toBe(new Date('2023-01-01T00:01:00Z').toISOString());
  });
});

import { renderHook } from '@testing-library/react';
import { useTheme } from '../useTheme';

jest.mock('react-native', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));

describe('useTheme', () => {
  it('returns light theme by default', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.dark).toBe(false);
    expect(result.current.colors.primary).toBe('#00796B');
  });

  it('returns dark theme when color scheme is dark', () => {
    const rn = require('react-native');
    rn.useColorScheme.mockReturnValue('dark');
    const { result } = renderHook(() => useTheme());
    expect(result.current.dark).toBe(true);
    expect(result.current.colors.background).toBe('#121212');
  });
});

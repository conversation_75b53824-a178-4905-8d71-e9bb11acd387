import { renderHook, act } from '@testing-library/react';
import { useSubscription } from '../useSubscription';

jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({ user: { id: 'user1' } }),
}));

jest.mock('@/services/stripe', () => ({
  getSubscriptionStatus: jest.fn(() => Promise.resolve({ isActive: true, status: 'active', currentPeriodEnd: null, cancelAtPeriodEnd: false })),
  createCheckoutSession: jest.fn(() => Promise.resolve({ sessionId: '1', url: 'u' })),
  handleWebCheckout: jest.fn(() => Promise.resolve()),
  handleMobileCheckout: jest.fn(() => Promise.resolve({ type: 'dismiss' })),
  validateSubscription: jest.fn(() => Promise.resolve({ needsRefresh: false })),
  SUBSCRIPTION_PRICES: { monthly: 'price' },
  clearSubscriptionCache: jest.fn(() => Promise.resolve()),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
}));

describe.skip('useSubscription', () => {
  it('loads subscription and purchases', async () => {
    const { result } = renderHook(() => useSubscription());
    await act(async () => {
      // wait effects
    });
    expect(result.current.subscription.isActive).toBe(true);

    await act(async () => {
      await result.current.purchaseSubscription();
    });
    expect(result.current.error).toBeNull();
  });
});

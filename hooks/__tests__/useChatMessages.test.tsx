import { renderHook, act } from '@testing-library/react';
import { useChatMessages } from '../useChatMessages';

jest.mock('uuid', () => ({ v4: () => 'uuid' + Math.random() }));

jest.mock('@/hooks/useChatHistory', () => ({
  useChatHistory: () => ({
    createChat: jest.fn(() => 'chat1'),
    updateChat: jest.fn(),
  }),
}));

jest.mock('@/hooks/useChatStorage', () => ({
  useChatStorage: () => ({
    getActiveChatId: jest.fn(() => Promise.resolve(null)),
    setActiveChatId: jest.fn(() => Promise.resolve()),
  }),
}));

describe.skip('useChatMessages', () => {
  it('sends message and streams response', async () => {
    jest.useFakeTimers();
    const { result } = renderHook(() => useChatMessages());

    await act(async () => {
      await result.current.sendMessage('hi');
    });

    // fast-forward streaming simulation
    jest.runAllTimers();

    expect(result.current.messages.length).toBe(2);
    expect(result.current.messages[0].role).toBe('user');
    expect(result.current.messages[1].role).toBe('assistant');
    expect(result.current.isStreaming).toBe(false);
  });
});

# Medical Advisor App

This repository contains the Medical Advisor application built with Next.js and Expo.

## Running Tests in VS Code

1. **Install Dependencies**

   Make sure you have [pnpm](https://pnpm.io/) installed. Install all project dependencies by running:

   ```bash
   pnpm install
   ```

2. **Run Tests**

   Execute the full Jest test suite from the VS Code terminal with:

   ```bash
   pnpm test
   ```

   This command uses <PERSON><PERSON> to run all unit tests and generates a code coverage report.

3. **Coverage Output**

   After the tests complete, coverage information can be found in the `coverage` directory. Open `coverage/lcov-report/index.html` in your browser to view the HTML report.

4. **Recommended VS Code Extensions**

   - **Jest** – shows test results inline and allows running or debugging tests directly from the editor.
   - **Coverage Gutters** – highlights covered and uncovered lines based on Je<PERSON>'s coverage output.

   Ensure VS Code is using the workspace TypeScript version for best IntelliSense results.

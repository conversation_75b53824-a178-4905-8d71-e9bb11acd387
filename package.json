{"name": "my-v0-project", "version": "0.1.0", "private": true, "main": "App.tsx", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --coverage"}, "dependencies": {"@babel/core": "latest", "@expo/vector-icons": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@react-native-async-storage/async-storage": "latest", "@react-navigation/drawer": "latest", "@react-navigation/native": "latest", "@rneui/base": "latest", "@rneui/themed": "latest", "@shopify/restyle": "latest", "@stripe/stripe-js": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "latest", "dotenv": "latest", "embla-carousel-react": "latest", "expo": "latest", "expo-build-properties": "^0.14.6", "expo-constants": "latest", "expo-font": "latest", "expo-linear-gradient": "latest", "expo-linking": "latest", "expo-router": "latest", "expo-status-bar": "latest", "expo-web-browser": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "lucide-react-native": "latest", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-native": "latest", "react-native-event-source": "latest", "react-native-gesture-handler": "latest", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "latest", "react-native-safe-area-context": "latest", "react-native-screens": "latest", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "latest", "stripe": "14.12.0", "supabase": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "latest", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/react": "^14", "@testing-library/react-native": "^12", "@types/jest": "^29", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "jest": "^29", "jest-expo": "^49", "postcss": "^8", "react-test-renderer": "^19", "tailwindcss": "^3.4.17", "ts-jest": "^29", "typescript": "^5"}}
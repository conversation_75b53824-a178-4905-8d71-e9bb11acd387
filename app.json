{"expo": {"name": "Medical Advisor", "slug": "medical-advisor", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "medicaladvisor", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.yourcompany.medicaladvisor", "infoPlist": {"GADApplicationIdentifier": "ca-app-pub-3940256099942544~**********", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "cstr6suwn9.skadnetwork"}], "NSUserTrackingUsageDescription": "This identifier will be used to deliver personalized ads to you.", "NSCameraUsageDescription": "This app uses the camera to scan health documents.", "NSPhotoLibraryUsageDescription": "This app uses photos to upload health documents."}}, "android": {"package": "com.yourcompany.medicaladvisor", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.INTERNET", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}}}